using System;
using System.Collections.Generic;
using OCRTools.Language;

namespace OCRTools.Test
{
    public class TranslateTest
    {
        public static void TestNewTranslateAPI()
        {
            try
            {
                Console.WriteLine("开始测试新的Microsoft Translator API...");
                
                // 测试翻译功能
                var testTexts = new List<string> { "你好", "世界", "测试" };
                var result = LanguageHelper.TransLanguage(testTexts, "en-US");
                
                Console.WriteLine("翻译结果:");
                foreach (var item in result)
                {
                    Console.WriteLine($"原文: {item.Key} -> 译文: {item.Value}");
                }
                
                Console.WriteLine("测试完成!");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"测试失败: {ex.Message}");
                Console.WriteLine($"详细错误: {ex}");
            }
        }
    }
}
