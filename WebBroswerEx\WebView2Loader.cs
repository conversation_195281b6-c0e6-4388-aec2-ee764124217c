using System;
using System.Linq;
using System.Reflection;
using System.Threading;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace OCRTools.WebBroswerEx
{
    /// <summary>
    /// WebView2加载器 - 基于Chromium内核的现代网页加载
    /// </summary>
    public class WebView2Loader : IWebLoader
    {
        // WebView2初始化超时时间（毫秒）
        private const int CORE_WEBVIEW2_TIMEOUT_MS = 2000;
        private Control _webView2Control;
        private object _coreWebView2;

        private bool _isInitialized = false;
        private bool _isDisposed = false;
        private bool _isInitializing = false;

        public string LoaderName => "WebView2 (Chromium)";
        public Control WebControl => _webView2Control;
        public string CurrentUrl { get; private set; } = string.Empty;
        public string DocumentTitle { get; private set; } = string.Empty;
        public bool IsInitialized => _isInitialized;

        public event EventHandler<string> LoadingStarted;
        public event EventHandler<LoadCompletedEventArgs> LoadingCompleted;
        public event EventHandler<string> TitleChanged;

        public async Task<bool> InitializeAsync()
        {
            // 防止重复初始化
            if (_isInitialized)
            {
                return _coreWebView2 != null;
            }

            if (_isInitializing)
            {
                // 如果正在初始化，直接返回false，让上层处理
                return false;
            }

            _isInitializing = true;

            try
            {
                // 1. 直接创建控件（SmartWebControl已经确认WebView2可用）
                _webView2Control = await ApplicationWebManager.CreateWebView2ControlAsync();
                if (_webView2Control == null)
                {
                    return false;
                }

                // 2. 使用ApplicationWebManager初始化CoreWebView2（带超时控制）
                var timeoutCts = new System.Threading.CancellationTokenSource(CORE_WEBVIEW2_TIMEOUT_MS);

                try
                {
                    var initTask = ApplicationWebManager.InitializeCoreWebView2Async(_webView2Control);
                    var timeoutTask = Task.Delay(CORE_WEBVIEW2_TIMEOUT_MS, timeoutCts.Token);

                    var completedTask = await Task.WhenAny(initTask, timeoutTask);

                    if (completedTask == timeoutTask)
                    {
                        // 超时了
                        await CleanupPartialInitialization();
                        return false;
                    }

                    // 正常完成，获取结果
                    _coreWebView2 = await initTask;
                    timeoutCts.Cancel(); // 取消超时任务
                }
                catch (System.Threading.Tasks.TaskCanceledException)
                {
                    await CleanupPartialInitialization();
                    return false;
                }

                if (_coreWebView2 == null)
                {
                    await CleanupPartialInitialization();
                    return false;
                }

                // 3. 配置设置和事件
                SetupBasicSettings();
                BindBasicEvents();

                _isInitialized = true;
                return true;
            }
            catch
            {
                await CleanupPartialInitialization();
                return false;
            }
            finally
            {
                _isInitializing = false;
            }
        }

        /// <summary>
        /// 清理部分初始化的资源
        /// </summary>
        private async Task CleanupPartialInitialization()
        {
            try
            {
                _coreWebView2 = null;
                _webView2Control?.Dispose();
                _webView2Control = null;
            }
            catch { }
        }

        /// <summary>
        /// 配置WebView2基本设置
        /// </summary>
        private bool SetupBasicSettings()
        {
            try
            {
                if (_coreWebView2 == null) return false;

                var coreType = _coreWebView2.GetType();
                var settingsProperty = coreType.GetProperty("Settings");
                var settings = settingsProperty?.GetValue(_coreWebView2);

                if (settings == null) return false;

                var settingsType = settings.GetType();
                var settingsApplied = 0;

                // 禁用默认上下文菜单
                var contextMenuProperty = settingsType.GetProperty("AreDefaultContextMenusEnabled");
                if (contextMenuProperty != null)
                {
                    contextMenuProperty.SetValue(settings, false);
                    settingsApplied++;
                }

                // 禁用开发者工具
                var devToolsProperty = settingsType.GetProperty("AreDevToolsEnabled");
                if (devToolsProperty != null)
                {
                   devToolsProperty.SetValue(settings, false);
                   settingsApplied++;
                }

                // 禁用浏览器快捷键
                var browserAcceleratorKeysProperty = settingsType.GetProperty("AreBrowserAcceleratorKeysEnabled");
                if (browserAcceleratorKeysProperty != null)
                {
                   browserAcceleratorKeysProperty.SetValue(settings, false);
                   settingsApplied++;
                }

                return settingsApplied > 0;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// 绑定WebView2基本事件
        /// </summary>
        private bool BindBasicEvents()
        {
            try
            {
                if (_coreWebView2 == null)
                {
                    return false;
                }

                var coreType = _coreWebView2.GetType();
                var eventsBinding = 0;

                // 定义要绑定的事件
                var eventBindings = new[]
                {
                    new { EventName = "NavigationStarting", HandlerMethod = nameof(OnNavigationStarting) },
                    new { EventName = "DOMContentLoaded", HandlerMethod = nameof(OnDOMContentLoaded) },
                    new { EventName = "NavigationCompleted", HandlerMethod = nameof(OnNavigationCompleted) },
                    new { EventName = "DocumentTitleChanged", HandlerMethod = nameof(OnDocumentTitleChanged) }
                };

                foreach (var binding in eventBindings)
                {
                    if (TryBindEvent(coreType, binding.EventName, binding.HandlerMethod))
                    {
                        eventsBinding++;
                    }
                }

                return eventsBinding > 0;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// 尝试绑定单个事件
        /// </summary>
        private bool TryBindEvent(Type coreType, string eventName, string handlerMethodName)
        {
            try
            {
                var eventInfo = coreType.GetEvent(eventName);
                if (eventInfo == null) return false;

                var method = typeof(WebView2Loader).GetMethod(handlerMethodName, BindingFlags.NonPublic | BindingFlags.Instance);
                if (method == null) return false;

                var handler = Delegate.CreateDelegate(eventInfo.EventHandlerType, this, method);
                eventInfo.AddEventHandler(_coreWebView2, handler);

                return true;
            }
            catch
            {
                return false;
            }
        }

        public async Task<bool> LoadAsync(string url, string postData = null)
        {
            if (!_isInitialized || _isDisposed) return false;

            try
            {
                url = PreProcessUrl(url);
                CurrentUrl = url;

                if (_coreWebView2 != null)
                {
                    if (!string.IsNullOrEmpty(postData))
                    {
                        return await NavigateWithWebResourceRequest(url, postData);
                    }
                    else
                    {
                        // GET请求处理
                        var navigateMethod = _coreWebView2.GetType().GetMethod("Navigate", new Type[] { typeof(string) });
                        navigateMethod?.Invoke(_coreWebView2, new object[] { url });
                        return true;
                    }
                }
            }
            catch (Exception ex)
            {
                LoadingCompleted?.Invoke(this, new LoadCompletedEventArgs(url, false, ex.Message));
            }
            return false;
        }

        /// <summary>
        /// 使用NavigateWithWebResourceRequest进行POST导航（最佳方案）
        /// </summary>
        private async Task<bool> NavigateWithWebResourceRequest(string url, string postData)
        {
            try
            {
                var coreType = _coreWebView2.GetType();

                // 获取Environment对象
                var environmentProperty = coreType.GetProperty("Environment");
                var environment = environmentProperty?.GetValue(_coreWebView2);

                if (environment != null)
                {
                    var environmentType = environment.GetType();

                    // 创建WebResourceRequest - 需要4个参数：uri, method, postData, headers
                    var createRequestMethod = environmentType.GetMethod("CreateWebResourceRequest");
                    if (createRequestMethod != null)
                    {
                        var postDataBytes = System.Text.Encoding.UTF8.GetBytes(postData);
                        var memoryStream = new System.IO.MemoryStream(postDataBytes);
                        var headers = "Content-Type: application/x-www-form-urlencoded\r\n";

                        var request = createRequestMethod.Invoke(environment, new object[] { url, "POST", memoryStream, headers });

                        if (request != null)
                        {
                            // 使用NavigateWithWebResourceRequest进行导航
                            var navigateWithRequestMethod = coreType.GetMethod("NavigateWithWebResourceRequest");
                            if (navigateWithRequestMethod != null)
                            {
                                navigateWithRequestMethod.Invoke(_coreWebView2, new object[] { request });
                                return true;
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"NavigateWithWebResourceRequest error: {ex.Message}");
            }

            return false;
        }

        /// <summary>
        /// WebView2导航开始事件处理
        /// </summary>
        [System.Reflection.Obfuscation(Exclude = true)]
        private void OnNavigationStarting(object sender, object e)
        {
            try
            {
                var url = GetEventProperty(e, "Uri")?.ToString() ?? string.Empty;
                CurrentUrl = url;
                LoadingStarted?.Invoke(this, url);
            }
            catch
            {
            }
        }

        /// <summary>
        /// WebView2 DOM内容加载完成事件处理 - 页面可交互时触发
        /// </summary>
        [System.Reflection.Obfuscation(Exclude = true)]
        private void OnDOMContentLoaded(object sender, object e)
        {
            try
            {
                LoadingCompleted?.Invoke(this, new LoadCompletedEventArgs(CurrentUrl, true));
            }
            catch
            {
            }
        }

        /// <summary>
        /// WebView2导航完成事件处理
        /// </summary>
        [System.Reflection.Obfuscation(Exclude = true)]
        private void OnNavigationCompleted(object sender, object e)
        {
            try
            {
                var isSuccess = GetEventProperty(e, "IsSuccess") as bool? ?? true;
                LoadingCompleted?.Invoke(this, new LoadCompletedEventArgs(CurrentUrl, isSuccess));
            }
            catch
            {
            }
        }

        /// <summary>
        /// WebView2文档标题变化事件处理
        /// </summary>
        [System.Reflection.Obfuscation(Exclude = true)]
        private void OnDocumentTitleChanged(object sender, object e)
        {
            try
            {
                if (_coreWebView2 != null)
                {
                    var coreType = _coreWebView2.GetType();
                    var titleProperty = coreType.GetProperty("DocumentTitle");
                    var title = titleProperty?.GetValue(_coreWebView2) as string ?? string.Empty;
                    DocumentTitle = title;
                    TitleChanged?.Invoke(this, title);
                }
            }
            catch
            {
            }
        }

        private object GetEventProperty(object eventArgs, string propertyName)
        {
            try
            {
                return eventArgs?.GetType().GetProperty(propertyName)?.GetValue(eventArgs);
            }
            catch
            {
                return null;
            }
        }

        public async Task<string> ExecuteScriptAsync(string script)
        {
            if (!_isInitialized || _isDisposed || _coreWebView2 == null || string.IsNullOrWhiteSpace(script))
            {
                return null;
            }

            try
            {
                var executeScriptMethod = _coreWebView2.GetType().GetMethod("ExecuteScriptAsync", new Type[] { typeof(string) });
                if (executeScriptMethod == null)
                {
                    return null;
                }

                var task = executeScriptMethod.Invoke(_coreWebView2, new object[] { script }) as Task;
                if (task is Task taskObj)
                {
                    await taskObj;

                    dynamic dynamicTask = task;
                    return ProcessScriptResult(dynamicTask.Result as string);
                }
            }
            catch (System.Runtime.InteropServices.COMException)
            {
            }
            catch (Exception)
            {
            }
            return null;
        }

        private string ProcessScriptResult(string result)
        {
            if (string.IsNullOrEmpty(result) || result == "null") return null;

            // 处理JSON字符串格式
            if (result.StartsWith("\"") && result.EndsWith("\""))
            {
                try
                {
                    return result.Substring(1, result.Length - 2).Replace("\\\"", "\"").Replace("\\\\", "\\");
                }
                catch
                {
                    return result;
                }
            }

            return result;
        }

        private string PreProcessUrl(string url)
        {
            if (string.IsNullOrWhiteSpace(url))
                return "about:blank";

            try
            {
                return CommonMethod.PreProcessUrl(url);
            }
            catch
            {
                return url;
            }
        }

        public void Dispose()
        {
            if (!_isDisposed)
            {
                try
                {
                    _webView2Control?.Dispose();
                    _webView2Control = null;
                    _coreWebView2 = null;
                }
                catch
                {
                }
                finally
                {
                    _isDisposed = true;
                }
            }
        }
    }
}
