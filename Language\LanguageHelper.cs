using MetroFramework.Forms;
using OCRTools.Common;
using OCRTools.Properties;
using System;
using System.Collections;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using System.Web;
using System.Windows.Forms;

namespace OCRTools.Language
{
    public static class LanguageHelper
    {
        public const string StrBaseLanguage = "zh-CN";

        public static string NowLanguage = StrBaseLanguage;

        public static bool IsChinese { get { return Equals(StrBaseLanguage, NowLanguage); } }

        #region Init Form Text

        public static void InitLanguage(this Form form)
        {
            InitFormText(form, true);
        }

        public static void InitFormText(Form form, bool isSet)
        {
            InitTextInfo(form, new List<Control> { form }, isSet);
        }

        public static void InitTextInfo(Form form, IEnumerable controls, bool isSet)
        {
            foreach (Control control in controls)
            {
                if (control is NumericUpDown)
                {
                    continue;
                }
                //if (!isSet && control.AccessibleDescription == null)
                if (control.AccessibleDescription == null)
                {
                    control.AccessibleDescription = control.Text;
                }
                if (isSet && !(control is WebBrowser) && !(control is TextBox))
                {
                    var strNewText = string.Empty;
                    if (control is ComboBox || control is RichTextBox)
                    {
                        if (!string.IsNullOrWhiteSpace(control.Text))
                        {
                            strNewText = control.Text.CurrentText();
                        }
                    }
                    else
                    {
                        if (!string.IsNullOrWhiteSpace(control.AccessibleDescription))
                        {
                            strNewText = control.AccessibleDescription.CurrentText();
                        }
                    }
                    if (!string.IsNullOrWhiteSpace(strNewText))
                    {
                        control.Text = strNewText;
                    }
                }
                if (control is ToolStrip strip)
                {
                    InitToolStripMenuItem(form, strip.Items, isSet);
                }
                else if (control is DataGridView grid)
                {
                    InitDataGridViewColumn(form, grid.Columns, isSet);
                }
                else if (control is MenuButton mbtn && mbtn.Menu != null)
                {
                    InitToolStripMenuItem(form, mbtn.Menu.Items, isSet);
                }
                //else if (control is ComboBox combox)
                //{
                //    InitComboBoxItem(combox, isSet);
                //}
                if (control.ContextMenuStrip != null)
                {
                    InitToolStripMenuItem(form, control.ContextMenuStrip.Items, isSet);
                }

                if (control.Controls.Count > 0)
                    InitTextInfo(form, control.Controls, isSet);
            }
        }

        private static void InitDataGridViewColumn(Form form, DataGridViewColumnCollection items, bool isSet)
        {
            foreach (DataGridViewColumn item in items)
            {
                //if (!isSet)
                {
                    if (item.Tag == null)
                    {
                        item.Tag = item.HeaderText;
                    }
                }
                //else
                {
                    if (item.Tag != null && !string.IsNullOrWhiteSpace(item.Tag.ToString()))
                    {
                        item.HeaderText = item.Tag.ToString().CurrentText();
                    }
                }
            }
        }

        private static void InitToolStripMenuItem(Form form, ToolStripItemCollection items, bool isSet)
        {
            ToolStripItem[] itemsArray = null;

            for (int i = 0; i < 3; i++)
            {
                try
                {
                    itemsArray = new ToolStripItem[items.Count];
                    items.CopyTo(itemsArray, 0);
                }
                catch (InvalidOperationException)
                {
                    System.Threading.Thread.Sleep(50);
                }
                if (itemsArray?.Length > 0)
                {
                    break;
                }
            }

            if (itemsArray?.Length > 0)
            {
                foreach (ToolStripItem item in itemsArray)
                {
                    if (item.IsDisposed) continue;

                    ProcessToolStripItem(form, item, isSet);
                }
            }
        }

        private static void ProcessToolStripItem(Form form, ToolStripItem item, bool isSet)
        {
            try
            {
                //if (!isSet)
                {
                    if (item.AccessibleDescription == null)
                    {
                        item.AccessibleDescription = item.Text;
                    }
                    if (item.AccessibleName == null && !string.IsNullOrWhiteSpace(item.ToolTipText) && !Equals(item.Text, item.ToolTipText))
                    {
                        if (item.ToolTipText.Length < 20)
                            item.AccessibleName = item.ToolTipText;
                    }
                }
                //else
                {
                    var strNewText = "";
                    if (!string.IsNullOrWhiteSpace(item.AccessibleDescription))
                    {
                        strNewText = item.AccessibleDescription.CurrentText();
                    }
                    if (!string.IsNullOrWhiteSpace(strNewText))
                    {
                        item.Text = strNewText;
                    }

                    if (!string.IsNullOrWhiteSpace(item.AccessibleName))
                    {
                        item.ToolTipText = item.AccessibleName.CurrentText();
                    }
                    //else if (!string.IsNullOrWhiteSpace(item.Text))
                    //{
                    //    item.Text = item.Text.CurrentText();
                    //}
                }

                if (item is ToolStripDropDownItem tsm)
                {
                    InitToolStripMenuItem(form, tsm.DropDownItems, isSet);
                }
            }
            catch (ObjectDisposedException)
            {
                // 忽略已释放的对象
            }
            catch (InvalidOperationException)
            {
                // 忽略无效操作异常
            }
        }

        #endregion

        public static void InstallLanguage(SupportedLanguage languageType, string language)
        {
            var strName = languageType.GetValue<DescriptionAttribute>("Description") + " " + "语言包".CurrentText();
            var item = new ObjectTypeItem
            {
                Name = strName,
                AppPath = CommonString.DefaultLanguagePath,
                UpdateUrl = string.Format("{0}lang/{1}.lang?t=" + ServerTime.DateTime.Ticks, CommonString.HostUpdate?.FullUrl, language),
                Date = CommonMethod.GetLastWriteDate(CommonString.DefaultLanguagePath + language + ".lang"),
                Desc = strName
            };
            var result = CommonUpdate.Install(item.UpdateUrl, item.Date, item.Desc, item.Name, item.AppPath, false, true, true, false, null, true);
        }

        static Dictionary<string, string> DicNowCulture = new Dictionary<string, string>();
        static Dictionary<string, Dictionary<string, string>> DicStaticCulture = new Dictionary<string, Dictionary<string, string>>();

        static LanguageHelper()
        {
#if DEBUG
            LoadBaseCulture();
#endif
            var strStaticLang = "{\"下载完成\":{\"zh-TW\":\"下載完成\",\"en-US\":\"Download complete\",\"ja-JP\":\"ダウンロードが完了しました\",\"ko-KR\":\"다운로드가 완료되었습니다\",\"fr-FR\":\"Téléchargement terminé\",\"de-DE\":\"Download abgeschlossen\",\"ru-RU\":\"Загрузить полностью\",\"uk-UA\":\"Завантажити повністю\",\"ar-SA\":\"اكتمل التنزيل\",\"es-ES\":\"Descargar completa\",\"th-TH\":\"ดาวน์โหลดเสร็จสมบูรณ์\",\"vi-VN\":\"Tải xuống hoàn tất\",\"pt-PT\":\"Download completo\",\"he-IL\":\"ההורדה הושלמה\",\"hu-HU\":\"Letöltés kész\",\"id-ID\":\"Unduh selesai\",\"it-IT\":\"Download completato\",\"fa-IR\":\"دانلود کامل شد\",\"pl-PL\":\"Pobierz kompletne\",\"ro-RO\":\"Descarcă complet\",\"tr-TR\":\"İndirme işlemi tamamlandı\",\"nl-NL\":\"Volledig downloaden\",\"hi-IN\":\"डाउनलोड पूर्ण\",\"ta-IN\":\"பதிவிறக்கம் நிறைவடைந்தது\",\"sr-Latn-RS\":\"Preuzimanje je dovršeno\",\"sv-SE\":\"Nedladdningen är klar\",\"ms-MY\":\"Muat turun lengkap\",\"cs\":\"Stahování dokončeno\",\"km-KH\":\"ទាញយកទាំងស្រុង\",\"ka\":\"ჩამოტვირთვა დასრულებულია\"},\"开始安装\":{\"zh-TW\":\"開始安裝\",\"en-US\":\"Start install\",\"ja-JP\":\"インストールを開始する\",\"ko-KR\":\"설치 시작\",\"fr-FR\":\"Démarrer l’installation\",\"de-DE\":\"Starten Sie die Installation\",\"ru-RU\":\"Начать установку\",\"uk-UA\":\"Почніть інсталяцію\",\"ar-SA\":\"بدء التثبيت\",\"es-ES\":\"Iniciar instalación\",\"th-TH\":\"เริ่มการติดตั้ง\",\"vi-VN\":\"Bắt đầu cài đặt\",\"pt-PT\":\"Iniciar instalação\",\"he-IL\":\"התחל בהתקנה\",\"hu-HU\":\"Telepítés indítása\",\"id-ID\":\"Mulai instal\",\"it-IT\":\"Avvia installazione\",\"fa-IR\":\"شروع نصب\",\"pl-PL\":\"Rozpocznij instalację\",\"ro-RO\":\"Începeți instalarea\",\"tr-TR\":\"Yüklemeyi başlat\",\"nl-NL\":\"Begin met installeren\",\"hi-IN\":\"स्थापना प्रारंभ करें\",\"ta-IN\":\"நிறுவலைத் தொடங்கு\",\"sr-Latn-RS\":\"Započni instalaciju\",\"sv-SE\":\"Starta installationen\",\"ms-MY\":\"Mulakan pemasangan\",\"cs\":\"Spustit instalaci\",\"km-KH\":\"ចាប់ផ្តើមដំឡើង\",\"ka\":\"ინსტალაციის დაწყება\"},\"开始更新\":{\"zh-TW\":\"開始更新\",\"en-US\":\"Start update\",\"ja-JP\":\"アップデートを開始する\",\"ko-KR\":\"업데이트 시작\",\"fr-FR\":\"Démarrer la mise à jour\",\"de-DE\":\"Update starten\",\"ru-RU\":\"Начать обновление\",\"uk-UA\":\"Почати оновлення\",\"ar-SA\":\"بدء التحديث\",\"es-ES\":\"Iniciar actualización\",\"th-TH\":\"เริ่มการอัปเดต\",\"vi-VN\":\"Bắt đầu cập nhật\",\"pt-PT\":\"Iniciar atualização\",\"he-IL\":\"התחל עדכון\",\"hu-HU\":\"Frissítés indítása\",\"id-ID\":\"Mulai pembaruan\",\"it-IT\":\"Avvia aggiornamento\",\"fa-IR\":\"اغاز بروزرسانی\",\"pl-PL\":\"Rozpocznij aktualizację\",\"ro-RO\":\"Începeți actualizarea\",\"tr-TR\":\"Güncellemeyi başlat\",\"nl-NL\":\"Update starten\",\"hi-IN\":\"अपडेट शुरू करें\",\"ta-IN\":\"புதுப்பித்தலைத் தொடங்கு\",\"sr-Latn-RS\":\"Započni ispravku\",\"sv-SE\":\"Starta uppdatering\",\"ms-MY\":\"Mulakan kemas kini\",\"cs\":\"Spustit aktualizaci\",\"km-KH\":\"ចាប់ផ្តើម update\",\"ka\":\"განახლების დაწყება\"},\"开始解压缩\":{\"zh-TW\":\"開始解壓縮\",\"en-US\":\"Start extracting\",\"ja-JP\":\"解凍を開始する\",\"ko-KR\":\"압축 풀기 시작\",\"fr-FR\":\"Démarrer l’extraction\",\"de-DE\":\"Beginnen Sie mit dem Extrahieren\",\"ru-RU\":\"Начать извлечение\",\"uk-UA\":\"Почніть видобування\",\"ar-SA\":\"ابدأ الاستخراج\",\"es-ES\":\"Empezar a extraer\",\"th-TH\":\"เริ่มสกัด\",\"vi-VN\":\"Bắt đầu giải nén\",\"pt-PT\":\"Comece a extrair\",\"he-IL\":\"התחל לחלץ\",\"hu-HU\":\"Indítsa el a kitermelést\",\"id-ID\":\"Mulai mengekstrak\",\"it-IT\":\"Inizia a estrarre\",\"fa-IR\":\"شروع به استخراج\",\"pl-PL\":\"Rozpocznij wyodrębnianie\",\"ro-RO\":\"Începeți extragerea\",\"tr-TR\":\"Ayıklamaya başla\",\"nl-NL\":\"Begin met extraheren\",\"hi-IN\":\"निकालना शुरू करें\",\"ta-IN\":\"பிரித்தெடுக்கத் தொடங்குங்கள்\",\"sr-Latn-RS\":\"Započni izdvajanje\",\"sv-SE\":\"Börja extrahera\",\"ms-MY\":\"Mula mengekstrak\",\"cs\":\"Spuštění extrahování\",\"km-KH\":\"ចាប់ផ្តើមស្រង់ចេញ\",\"ka\":\"დაიწყეთ მოპოვება\"},\"语言包\":{\"zh-TW\":\"語言包\",\"en-US\":\"Language Pack\",\"ja-JP\":\"言語パック\",\"ko-KR\":\"언어 팩\",\"fr-FR\":\"Pack de langue\",\"de-DE\":\"Sprachpaket\",\"ru-RU\":\"Языковой пакет\",\"uk-UA\":\"Мовний пакет\",\"ar-SA\":\"حزمة اللغة\",\"es-ES\":\"Paquete de idioma\",\"th-TH\":\"แพ็คภาษา\",\"vi-VN\":\"Gói ngôn ngữ\",\"pt-PT\":\"Pacote de idiomas\",\"he-IL\":\"ערכת שפה\",\"hu-HU\":\"Nyelvi csomag\",\"id-ID\":\"Paket Bahasa\",\"it-IT\":\"Pacchetto lingua\",\"fa-IR\":\"بسته زبان\",\"pl-PL\":\"Pakiet językowy\",\"ro-RO\":\"Pachet lingvistic\",\"tr-TR\":\"Dil Paketi\",\"nl-NL\":\"Taal Pakket\",\"hi-IN\":\"भाषा पैक\",\"ta-IN\":\"மொழி பேக்\",\"sr-Latn-RS\":\"Jezički paket\",\"sv-SE\":\"Språkpaket\",\"ms-MY\":\"Pek Bahasa\",\"cs\":\"Jazykový balíček\",\"km-KH\":\"កញ្ចប់ភាសា\",\"ka\":\"ენების პაკეტი\"},\"安装\":{\"zh-TW\":\"安裝\",\"en-US\":\"Install\",\"ja-JP\":\"取り付け\",\"ko-KR\":\"설치\",\"fr-FR\":\"Installer\",\"de-DE\":\"Installieren\",\"ru-RU\":\"Устанавливать\",\"uk-UA\":\"Інсталювати\",\"ar-SA\":\"أقام\",\"es-ES\":\"Instalar\",\"th-TH\":\"ติดตั้ง\",\"vi-VN\":\"Cài đặt\",\"pt-PT\":\"Instalar\",\"he-IL\":\"התקין\",\"hu-HU\":\"Felszerel\",\"id-ID\":\"Pasang\",\"it-IT\":\"Installare\",\"fa-IR\":\"نصب\",\"pl-PL\":\"Instalować\",\"ro-RO\":\"Instala\",\"tr-TR\":\"Yüklemek\",\"nl-NL\":\"Installeren\",\"hi-IN\":\"पदासीन करना\",\"ta-IN\":\"நிறுவு\",\"sr-Latn-RS\":\"Instalirate\",\"sv-SE\":\"Installera\",\"ms-MY\":\"Memasang\",\"cs\":\"Nainstalovat\",\"km-KH\":\"ដំឡើង\",\"ka\":\"ინსტალაცია\"},\"更新\":{\"zh-TW\":\"更新\",\"en-US\":\"Update\",\"ja-JP\":\"更新\",\"ko-KR\":\"업데이트\",\"fr-FR\":\"Mettre à jour\",\"de-DE\":\"Aktualisieren\",\"ru-RU\":\"Обновлять\",\"uk-UA\":\"Оновлювати\",\"ar-SA\":\"تحديث\",\"es-ES\":\"Actualizar\",\"th-TH\":\"อัพเดต\",\"vi-VN\":\"Cập nhật\",\"pt-PT\":\"Atualizar\",\"he-IL\":\"עדכון\",\"hu-HU\":\"Frissít\",\"id-ID\":\"Pemutakhiran\",\"it-IT\":\"Aggiornare\",\"fa-IR\":\"روز رسانی\",\"pl-PL\":\"Aktualizacja\",\"ro-RO\":\"Actualiza\",\"tr-TR\":\"Güncelleştirmek\",\"nl-NL\":\"Update\",\"hi-IN\":\"आधुनिकीकरणअ\",\"ta-IN\":\"புதுப்பி\",\"sr-Latn-RS\":\"Ažuriranje\",\"sv-SE\":\"Uppdatera\",\"ms-MY\":\"Kemaskini terakhir\",\"cs\":\"Aktualizace\",\"km-KH\":\"Update\",\"ka\":\"განახლება\"},\"已下载\":{\"zh-TW\":\"已下載\",\"en-US\":\"Downloaded\",\"ja-JP\":\"ダウンロード\",\"ko-KR\":\"다운로드\",\"fr-FR\":\"Téléchargé\",\"de-DE\":\"Gedownloadet\",\"ru-RU\":\"Загрузить\",\"uk-UA\":\"Завантажити\",\"ar-SA\":\"تحميل\",\"es-ES\":\"Descargó\",\"th-TH\":\"ดาวน์โหลด\",\"vi-VN\":\"Tải về\",\"pt-PT\":\"Baixado\",\"he-IL\":\"שהורדו\",\"hu-HU\":\"Letöltött\",\"id-ID\":\"Download\",\"it-IT\":\"Scaricato\",\"fa-IR\":\"دریافت\",\"pl-PL\":\"Pobrać\",\"ro-RO\":\"Descărcat\",\"tr-TR\":\"Indirilen\",\"nl-NL\":\"Gedownload\",\"hi-IN\":\"डाउनलोड\",\"ta-IN\":\"பதிவிறக்க\",\"sr-Latn-RS\":\"Preuzete\",\"sv-SE\":\"Hämtade\",\"ms-MY\":\"Dimuat turun\",\"cs\":\"Stažený\",\"km-KH\":\"ទាញយក\",\"ka\":\"გადმოწერილი\"},\"请稍候重试！\":{\"zh-TW\":\"請稍候重試！\",\"en-US\":\"Please try again later!\",\"ja-JP\":\"しばらくしてからもう一度お試しください。\",\"ko-KR\":\"나중에 다시 시도하십시오!\",\"fr-FR\":\"Veuillez réessayer plus tard !\",\"de-DE\":\"Bitte versuchen Sie es später noch einmal!\",\"ru-RU\":\"Пожалуйста, повторите попытку позже!\",\"uk-UA\":\"Будь ласка, спробуйте пізніше!\",\"ar-SA\":\"يرجى إعادة المحاولة لاحقا!\",\"es-ES\":\"¡Por favor, inténtelo de nuevo más tarde!\",\"th-TH\":\"โปรดลองอีกครั้งในภายหลัง!\",\"vi-VN\":\"Vui lòng thử lại sau!\",\"pt-PT\":\"Por favor, tente novamente mais tarde!\",\"he-IL\":\"נסה שוב מאוחר יותר!\",\"hu-HU\":\"Kérjük, próbálja újra később!\",\"id-ID\":\"Silakan coba lagi nanti!\",\"it-IT\":\"Riprova più tardi!\",\"fa-IR\":\"لطفا بعدا دوباره امتحان کنید!\",\"pl-PL\":\"Spróbuj ponownie później!\",\"ro-RO\":\"Vă rugăm să încercați din nou mai târziu!\",\"tr-TR\":\"Lütfen daha sonra tekrar deneyin!\",\"nl-NL\":\"Probeer het later opnieuw!\",\"hi-IN\":\"कृपया बाद में पुनः प्रयास करें!\",\"ta-IN\":\"தயவுசெய்து பின்னர் மீண்டும் முயற்சிக்கவும்!\",\"sr-Latn-RS\":\"Pokušajte ponovo kasnije!\",\"sv-SE\":\"Försök igen senare!\",\"ms-MY\":\"Sila cuba lagi kemudian!\",\"cs\":\"Zkuste to prosím později!\",\"km-KH\":\"សូមសាកល្បងម្តងទៀតនៅពេលក្រោយ!\",\"ka\":\"გთხოვთ, სცადოთ მოგვიანებით!\"},\"更新失败\":{\"zh-TW\":\"更新失敗\",\"en-US\":\"Update failed\",\"ja-JP\":\"更新に失敗しました\",\"ko-KR\":\"업데이트에 실패했습니다.\",\"fr-FR\":\"Échec de la mise à jour\",\"de-DE\":\"Update fehlgeschlagen\",\"ru-RU\":\"Ошибка обновления\",\"uk-UA\":\"Не вдалося оновити\",\"ar-SA\":\"فشل التحديث\",\"es-ES\":\"Error de actualización\",\"th-TH\":\"การอัปเดตล้มเหลว\",\"vi-VN\":\"Cập nhật không thành công\",\"pt-PT\":\"Falha na atualização\",\"he-IL\":\"העדכון נכשל\",\"hu-HU\":\"A frissítés sikertelen\",\"id-ID\":\"Pembaruan gagal\",\"it-IT\":\"Aggiornamento non riuscito\",\"fa-IR\":\"بروزرسانی شکست خورد\",\"pl-PL\":\"Aktualizacja nie powiodła się\",\"ro-RO\":\"Actualizarea nu a reușit\",\"tr-TR\":\"Güncelleme başarısız oldu\",\"nl-NL\":\"Update mislukt\",\"hi-IN\":\"अद्यतन विफल रहा\",\"ta-IN\":\"புதுப்பித்தல் தோல்வியுற்ற\",\"sr-Latn-RS\":\"Ažuriranje nije uspelo\",\"sv-SE\":\"Uppdateringen misslyckades\",\"ms-MY\":\"Kemas kini gagal\",\"cs\":\"Aktualizace se nezdařila\",\"km-KH\":\"ការ Update បរាជ័យ\",\"ka\":\"განახლება ვერ შედგა\"},\"立即安装\":{\"zh-TW\":\"立即安裝\",\"en-US\":\"Install now\",\"ja-JP\":\"今すぐインストールする\",\"ko-KR\":\"지금 설치하기\",\"fr-FR\":\"Installer maintenant\",\"de-DE\":\"Jetzt installieren\",\"ru-RU\":\"Установить сейчас\",\"uk-UA\":\"Встановити зараз\",\"ar-SA\":\"تثبيت الآن\",\"es-ES\":\"Instalar ahora\",\"th-TH\":\"ติดตั้งเดี๋ยวนี้\",\"vi-VN\":\"Cài đặt ngay\",\"pt-PT\":\"Instalar agora\",\"he-IL\":\"התקן כעת\",\"hu-HU\":\"Telepítés most\",\"id-ID\":\"Instal sekarang\",\"it-IT\":\"Installa ora\",\"fa-IR\":\"هماکنون نصب شود\",\"pl-PL\":\"Zainstaluj teraz\",\"ro-RO\":\"Instalează acum\",\"tr-TR\":\"Şimdi yükle\",\"nl-NL\":\"Installeer nu\",\"hi-IN\":\"अभी स्थापित करें\",\"ta-IN\":\"இப்போது நிறுவவும்\",\"sr-Latn-RS\":\"Instaliraj odmah\",\"sv-SE\":\"Installera nu\",\"ms-MY\":\"Pasang sekarang\",\"cs\":\"Nainstalovat nyní\",\"km-KH\":\"ដំឡើង ឥឡូវ\",\"ka\":\"დააინსტალირეთ ახლა\"},\"立即更新\":{\"zh-TW\":\"立即更新\",\"en-US\":\"Update now\",\"ja-JP\":\"今すぐ更新\",\"ko-KR\":\"지금 업데이트\",\"fr-FR\":\"Mettre à jour\",\"de-DE\":\"Jetzt aktualisieren\",\"ru-RU\":\"Обновить сейчас\",\"uk-UA\":\"Оновити зараз\",\"ar-SA\":\"التحديث الآن\",\"es-ES\":\"Actualiza ahora\",\"th-TH\":\"อัพเดตเดี๋ยวนี้\",\"vi-VN\":\"Cập nhật bây giờ\",\"pt-PT\":\"Atualizar agora\",\"he-IL\":\"תעדכן עכשיו\",\"hu-HU\":\"Frissíts most\",\"id-ID\":\"Perbarui sekarang\",\"it-IT\":\"Aggiorna\",\"fa-IR\":\"اکنون به روز رسانی شود\",\"pl-PL\":\"Aktualizuj teraz\",\"ro-RO\":\"Actualizaţi acum\",\"tr-TR\":\"Şimdi Güncelleştir\",\"nl-NL\":\"Nu bijwerken\",\"hi-IN\":\"अब अपडेट करो\",\"ta-IN\":\"இப்போது புதுப்பிக்கவும்\",\"sr-Latn-RS\":\"Ažuriraj sada\",\"sv-SE\":\"Uppdatera nu\",\"ms-MY\":\"Kemas kini sekarang\",\"cs\":\"Aktualizovat nyní\",\"km-KH\":\"ធ្វើបច្ចុប្បន្នភាពឥឡូវនេះ\",\"ka\":\"განაახლე\"},\"已安装成功！\":{\"zh-TW\":\"已安裝成功！\",\"en-US\":\"Installed successfully!\",\"ja-JP\":\"正常にインストールされました!\",\"ko-KR\":\"성공적으로 설치되었습니다!\",\"fr-FR\":\"Installé avec succès !\",\"de-DE\":\"Erfolgreich installiert!\",\"ru-RU\":\"Установили успешно!\",\"uk-UA\":\"Встановлено успішно!\",\"ar-SA\":\"تم التثبيت بنجاح!\",\"es-ES\":\"¡Instalado con éxito!\",\"th-TH\":\"ติดตั้งสําเร็จแล้ว!\",\"vi-VN\":\"Cài đặt thành công!\",\"pt-PT\":\"Instalado com sucesso!\",\"he-IL\":\"הותקן בהצלחה!\",\"hu-HU\":\"Sikeresen telepítve!\",\"id-ID\":\"Berhasil diinstal!\",\"it-IT\":\"Installato con successo!\",\"fa-IR\":\"با موفقیت نصب شد!\",\"pl-PL\":\"Zainstalowano pomyślnie!\",\"ro-RO\":\"Instalat cu succes!\",\"tr-TR\":\"Başarıyla kuruldu!\",\"nl-NL\":\"Succesvol geïnstalleerd!\",\"hi-IN\":\"सफलतापूर्वक स्थापित!\",\"ta-IN\":\"வெற்றிகரமாக நிறுவப்பட்டது!\",\"sr-Latn-RS\":\"Uspešno instaliran!\",\"sv-SE\":\"Installationen lyckades!\",\"ms-MY\":\"Dipasang dengan jayanya!\",\"cs\":\"Úspěšně nainstalováno!\",\"km-KH\":\"តម្លើងដោយជោគជ័យ!\",\"ka\":\"წარმატებით დაინსტალირებული!\"},\"OCR文字识别助手\":{\"zh-TW\":\"OCR文字識別助手\",\"en-US\":\"OCR Assistant\",\"ja-JP\":\"OCR文字認識アシスタント\",\"ko-KR\":\"OCR 문자 인식 도우미\",\"fr-FR\":\"OCR Assistant\",\"de-DE\":\"OCR-Assistent\",\"ru-RU\":\"Ассистент OCR\",\"uk-UA\":\"Асистент OCR\",\"ar-SA\":\"مساعد التعرف الضوئي على الحروف\",\"es-ES\":\"Asistente de OCR\",\"th-TH\":\"ผู้ช่วย OCR\",\"vi-VN\":\"Trợ lý OCR\",\"pt-PT\":\"Assistente de OCR\",\"he-IL\":\"עוזר זיהוי תווים אופטי (OCR)\",\"hu-HU\":\"OCR asszisztens\",\"id-ID\":\"Asisten OCR\",\"it-IT\":\"Assistente OCR\",\"fa-IR\":\"دستیار OCR\",\"pl-PL\":\"Asystent OCR\",\"ro-RO\":\"Asistent OCR\",\"tr-TR\":\"OCR Asistanı\",\"nl-NL\":\"OCR-assistent\",\"hi-IN\":\"ओसीआर सहायक\",\"ta-IN\":\"OCR உதவியாளர்\",\"sr-Latn-RS\":\"OCR pomoćnik\",\"sv-SE\":\"OCR-assistent\",\"ms-MY\":\"Pembantu OCR\",\"cs\":\"Asistent OCR\",\"km-KH\":\"ជំនួយការ OCR\",\"ka\":\"OCR ასისტენტი\"}}";
            DicStaticCulture = strStaticLang.DeserializeJson<Dictionary<string, Dictionary<string, string>>>();
        }

        private static SupportedLanguage GetDefaultCulture()
        {
            SupportedLanguage type = SupportedLanguage.English;
            try
            {
                string cultureName = CultureInfo.CurrentUICulture.Name;
                var lstSupportLanguages = Enum.GetValues(typeof(SupportedLanguage)).OfType<SupportedLanguage>();
                if (lstSupportLanguages.Any(p => Equals(p.GetValue<MenuCategoryAttribute>(), cultureName)))
                    type = lstSupportLanguages.FirstOrDefault(p => Equals(p.GetValue<MenuCategoryAttribute>(), cultureName));
                else if (lstSupportLanguages.Any(p => p.GetValue<MenuCategoryAttribute>().StartsWith(cultureName.Split('-')[0])))
                    type = lstSupportLanguages.FirstOrDefault(p => p.GetValue<MenuCategoryAttribute>().StartsWith(cultureName.Split('-')[0]));
            }
            catch { }
            return type;
        }

        public static void InitLanguage(string strLanguage)
        {
            //var sb = new StringBuilder();
            //foreach (var ci in CultureInfo.GetCultures(CultureTypes.InstalledWin32Cultures))
            //{
            //    sb.AppendLine(string.Format("{0}        {1}         {2}         {3}", ci, ci.NativeName, ci.EnglishName, ci.DisplayName));
            //}
#if DEBUG
            InitAllCulture(DicNowCulture, NowLanguage);
#endif
            SupportedLanguage type;
            if (string.IsNullOrWhiteSpace(strLanguage))
            {
                type = GetDefaultCulture();
            }
            else
            {
                Enum.TryParse(strLanguage, out type);

                if (Equals(type, SupportedLanguage.SimplifiedChinese) && !Equals(strLanguage, SupportedLanguage.SimplifiedChinese.ToString()))
                {
                    type = GetDefaultCulture();
                }
            }

            DicNowCulture = new Dictionary<string, string>();
            NowLanguage = type.GetValue<MenuCategoryAttribute>();

            if (!Equals(SupportedLanguage.SimplifiedChinese, type))
            {
                var langFile = CommonString.DefaultLanguagePath + NowLanguage + ".lang";

                if (!File.Exists(langFile) || Application.OpenForms.Count <= 0)
                    InstallLanguage(type, NowLanguage);

                if (File.Exists(langFile))
                {
                    try
                    {
                        DicNowCulture = File.ReadAllText(langFile, Encoding.UTF8).DeserializeJson<Dictionary<string, string>>();
                    }
                    catch (Exception oe)
                    {
                        Log.WriteError("InitLanguage", oe);
                        try
                        {
                            File.Delete(langFile);
                        }
                        catch { }
                    }
                }
                if (DicNowCulture.Count <= 0)
                {
                    type = SupportedLanguage.SimplifiedChinese;
                    NowLanguage = type.GetValue<MenuCategoryAttribute>();
                }
            }

            CommonSetting.SetValue("语言", type.ToString());

            CommonTranslate.RefreshLanguage();
#if DEBUG
            InitAllCulture(DicNowCulture, NowLanguage);
#endif
        }

#if DEBUG
        public static void ProcessTrans()
        {
            var transSelectedType = MessageBox.Show("是：内置翻译；否：用户翻译；", "选择类型", MessageBoxButtons.YesNoCancel);
            if (transSelectedType == DialogResult.Cancel)
                return;
            var frm = new Form() { StartPosition = FormStartPosition.CenterScreen, WindowState = FormWindowState.Maximized };

            var dgv = new DataGridView() { Top = 50, Width = frm.Width - 20, Height = frm.Height - 100, Anchor = AnchorStyles.Top | AnchorStyles.Bottom | AnchorStyles.Left | AnchorStyles.Right };
            dgv.DefaultCellStyle.WrapMode = DataGridViewTriState.True;
            dgv.RowTemplate.Height = 60;
            foreach (SupportedLanguage language in Enum.GetValues(typeof(SupportedLanguage)))
            {
                dgv.Columns.Add(language.GetValue<MenuCategoryAttribute>(), language.GetValue<DescriptionAttribute>("Description"));
                dgv.Columns[dgv.ColumnCount - 1].Width = 200;
            }
            dgv.Font = CommonString.GetSysNormalFont(15);
            dgv.ScrollBars = ScrollBars.Both;
            dgv.EditMode = DataGridViewEditMode.EditOnEnter;
            dgv.ColumnHeadersHeightSizeMode = DataGridViewColumnHeadersHeightSizeMode.EnableResizing;
            dgv.RowHeadersWidthSizeMode = DataGridViewRowHeadersWidthSizeMode.EnableResizing;
            frm.Controls.Add(dgv);
            dgv.RowPostPaint += (sender, e) =>
            {
                Rectangle rectangle = new Rectangle(e.RowBounds.Location.X,
                  e.RowBounds.Location.Y,
                  dgv.RowHeadersWidth - 4,
                  e.RowBounds.Height);
                TextRenderer.DrawText(e.Graphics, (e.RowIndex + 1).ToString(),
                  dgv.RowHeadersDefaultCellStyle.Font,
                  rectangle,
                  dgv.RowHeadersDefaultCellStyle.ForeColor,
                  TextFormatFlags.VerticalCenter | TextFormatFlags.Right);
            };

            var userTransType = "TransCache";

            var txtText = new TextBox() { Size = new Size(100, 50), Location = new Point(580, 0) };
            frm.Controls.Add(txtText);
            ProcessDataBind(dgv, transSelectedType == DialogResult.Yes, userTransType);

            var btnNext = new Button() { Text = "查找", Size = new Size(100, 50), Location = new Point(700, 0) };
            btnNext.Click += (sender, e) =>
            {
                var startIndex = dgv.CurrentCell == null ? 0 : dgv.CurrentCell.RowIndex + 1;
                dgv.CurrentCell = null;
                var strToFind = txtText.Text.Trim();

                if (!string.IsNullOrEmpty(strToFind))
                {
                    for (int i = startIndex; i < dgv.Rows.Count; i++)
                    {
                        if (dgv.CurrentCell != null)
                        {
                            break;
                        }
                        if (dgv.Rows[i].Cells[StrBaseLanguage].Value != null && dgv.Rows[i].Cells[StrBaseLanguage].Value.ToString().Contains(strToFind))
                        {
                            dgv.FirstDisplayedScrollingRowIndex = dgv.Rows[i].Index;
                            dgv.CurrentCell = dgv.Rows[i].Cells[StrBaseLanguage];
                        }
                    }
                }
                else
                {
                    foreach (DataGridViewColumn transItem in dgv.Columns)
                    {
                        if (dgv.CurrentCell != null)
                        {
                            break;
                        }
                        var transType = transItem.Name;
                        if (Equals(transType, StrBaseLanguage))
                        {
                            continue;
                        }
                        foreach (DataGridViewRow row in dgv.Rows)
                        {
                            if ((row.Cells[transType].Value == null || string.IsNullOrEmpty(row.Cells[transType].Value.ToString()))
                            && row.Cells[StrBaseLanguage].Value != null)
                            {
                                dgv.FirstDisplayedScrollingRowIndex = row.Index;
                                dgv.CurrentCell = row.Cells[transType];
                            }
                        }
                    }
                }
            };
            frm.Controls.Add(btnNext);
            txtText.KeyDown += (send, e) =>
            {
                if (e.KeyCode == Keys.Enter)
                {
                    btnNext.PerformClick();
                }
            };

            var btnTrans = new Button() { Text = "翻译", Size = new Size(100, 50), Location = new Point(100, 0) };
            var fromZhTo = new List<string> { "zh-TW", "ja-JP", "ko-KR", "en-US" };
            btnTrans.Click += (sender, e) =>
            {
                btnTrans.Enabled = false;
                Task.Factory.StartNew(() =>
                {
                    try
                    {
                        foreach (DataGridViewColumn transItem in dgv.Columns)
                        {
                            var transType = transItem.Name;
                            if (Equals(transType, StrBaseLanguage))
                            {
                                continue;
                            }
                            var lstToTrans = new Dictionary<string, int>();
                            foreach (DataGridViewRow row in dgv.Rows)
                            {
                                if ((row.Cells[transType].Value == null || string.IsNullOrEmpty(row.Cells[transType].Value.ToString()))
                                && row.Cells[StrBaseLanguage].Value != null)
                                {
                                    var fromLang = fromZhTo.Contains(transType) ? StrBaseLanguage : "en-US";
                                    if (row.Cells[fromLang].Value == null)
                                    {
                                        fromLang = StrBaseLanguage;
                                    }
                                    lstToTrans[row.Cells[fromLang].Value.ToString()] = row.Index;
                                }
                            }
                            if (lstToTrans.Count <= 0)
                            {
                                continue;
                            }
                            var dicTrans = TransLanguage(lstToTrans.Keys.ToList(), transType);
                            foreach (var item in dicTrans.Keys)
                            {
                                dgv.Rows[lstToTrans[item]].Cells[transType].Value = dicTrans[item];
                            }
                        }
                    }
                    catch (Exception oe)
                    {
                        Console.WriteLine(oe);
                    }
                    finally
                    {
                        btnTrans.Enabled = true;
                    }
                });
            };
            frm.Controls.Add(btnTrans);

            var btnSave = new Button() { Text = "保存", Size = new Size(100, 50), Location = new Point(220, 0) };
            btnSave.Click += (sender, e) =>
            {
                btnNext.PerformClick();
                if (dgv.CurrentCell != null)
                {
                    MessageBox.Show("还有未翻译部分！");
                    return;
                }
                if (transSelectedType == DialogResult.Yes)
                {
                    foreach (DataGridViewColumn transItem in dgv.Columns)
                    {
                        var transType = transItem.Name;
                        if (Equals(transType, StrBaseLanguage))
                        {
                            continue;
                        }
                        foreach (DataGridViewRow row in dgv.Rows)
                        {
                            if (row.Cells[StrBaseLanguage].Value != null)
                            {
                                if (!DicAllCulture.ContainsKey(row.Cells[StrBaseLanguage].Value.ToString()))
                                {
                                    DicAllCulture[row.Cells[StrBaseLanguage].Value.ToString()] = new Dictionary<string, string>();
                                }
                                DicAllCulture[row.Cells[StrBaseLanguage].Value.ToString()][transType] = row.Cells[transType].Value?.ToString() ?? "";
                            }
                        }
                    }
                    SaveAllCulture();
                }
                else
                {
                    var dicTmp = new Dictionary<string, Dictionary<string, string>>();
                    foreach (DataGridViewColumn transItem in dgv.Columns)
                    {
                        var transType = transItem.Name;
                        if (Equals(transType, StrBaseLanguage))
                        {
                            continue;
                        }
                        foreach (DataGridViewRow row in dgv.Rows)
                        {
                            if (row.Cells[StrBaseLanguage].Value != null)
                            {
                                if (!dicTmp.ContainsKey(row.Cells[StrBaseLanguage].Value.ToString()))
                                {
                                    dicTmp[row.Cells[StrBaseLanguage].Value.ToString()] = new Dictionary<string, string>();
                                }
                                dicTmp[row.Cells[StrBaseLanguage].Value.ToString()][transType] = row.Cells[transType].Value?.ToString() ?? "";
                            }
                        }
                    }
                    File.WriteAllText(Application.StartupPath + "\\" + userTransType, "\"" + EscapeString(CommonString.JavaScriptSerializer.Serialize(dicTmp)) + "\"", Encoding.UTF8);
                }
                MessageBox.Show("保存成功", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
            };
            frm.Controls.Add(btnSave);

            var btnDelete = new Button() { Text = "删除", Size = new Size(100, 50), Location = new Point(340, 0) };
            btnDelete.Click += (sender, e) =>
            {
                if (dgv.CurrentRow == null)
                {
                    return;
                }
                dgv.Rows.Remove(dgv.CurrentRow);
            };
            frm.Controls.Add(btnDelete);

            var btnCopy = new Button() { Text = "复制", Size = new Size(100, 50), Location = new Point(460, 0) };
            btnCopy.Click += (sender, e) =>
            {
                if (dgv.CurrentCell == null)
                {
                    return;
                }
                var value = dgv.CurrentCell.Value?.ToString();

                for (int i = dgv.CurrentCell.ColumnIndex + 1; i < dgv.Columns.Count; i++)
                {
                    dgv.Rows[dgv.CurrentCell.RowIndex].Cells[i].Value = value;
                }
            };
            frm.Controls.Add(btnCopy);

            var btnInit = new Button() { Text = "加载", Size = new Size(100, 50), Location = new Point(820, 0) };
            btnInit.Click += (sender, e) =>
            {
                if (!string.IsNullOrEmpty(txtText.Text))
                {
                    userTransType = txtText.Text.Trim();
                    txtText.Text = "";
                    ProcessDataBind(dgv, transSelectedType == DialogResult.Yes, userTransType);
                }
            };
            frm.Controls.Add(btnInit);

            CommonMethod.EnableDoubleBuffering(frm);
            frm.Show();
        }

        private static bool NeedEscape(string src, int i)
        {
            char c = src[i];
            return c < 32 || c == '"' || c == '\\'
                // Broken lead surrogate
                || (c >= '\uD800' && c <= '\uDBFF' &&
                    (i == src.Length - 1 || src[i + 1] < '\uDC00' || src[i + 1] > '\uDFFF'))
                // Broken tail surrogate
                || (c >= '\uDC00' && c <= '\uDFFF' &&
                    (i == 0 || src[i - 1] < '\uD800' || src[i - 1] > '\uDBFF'))
                // To produce valid JavaScript
                || c == '\u2028' || c == '\u2029'
                // Escape "</" for <script> tags
                || (c == '/' && i > 0 && src[i - 1] == '<');
        }

        public static string EscapeString(string src)
        {
            var sb = new StringBuilder();
            int start = 0;
            for (int i = 0; i < src.Length; i++)
                if (NeedEscape(src, i))
                {
                    sb.Append(src, start, i - start);
                    switch (src[i])
                    {
                        case '\b': sb.Append("\\b"); break;
                        case '\f': sb.Append("\\f"); break;
                        case '\n': sb.Append("\\n"); break;
                        case '\r': sb.Append("\\r"); break;
                        case '\t': sb.Append("\\t"); break;
                        case '\"': sb.Append("\\\""); break;
                        case '\\': sb.Append("\\\\"); break;
                        case '/': sb.Append("\\/"); break;
                        default:
                            sb.Append("\\u");
                            sb.Append(((int)src[i]).ToString("x04"));
                            break;
                    }
                    start = i + 1;
                }
            sb.Append(src, start, src.Length - start);
            return sb.ToString();
        }

        private static void ProcessDataBind(DataGridView dgv, bool isInner, string type)
        {
            dgv.Rows.Clear();
            if (!isInner && !string.IsNullOrEmpty(type))
            {
                var dicTmp = new Dictionary<string, Dictionary<string, string>>();
                if (File.Exists(type))
                {
                    var strTmp = File.ReadAllText(type, Encoding.UTF8).TrimStart('\"').TrimEnd('\"');
                    strTmp = Regex.Unescape(strTmp);
                    dicTmp = strTmp.DeserializeJson<Dictionary<string, Dictionary<string, string>>>();
                    foreach (var item in dicTmp.Keys)
                    {
                        int index = dgv.Rows.Add();
                        for (int i = 0; i < dgv.Columns.Count; i++)
                        {
                            if (Equals(dgv.Columns[i].Name, StrBaseLanguage))
                                dgv.Rows[index].Cells[dgv.Columns[i].Name].Value = item;
                            else
                            {
                                dicTmp[item].TryGetValue(dgv.Columns[i].Name, out string strValue);
                                if (string.IsNullOrEmpty(strValue) && DicAllCulture.ContainsKey(item))
                                    DicAllCulture[item].TryGetValue(dgv.Columns[i].Name, out strValue);
                                dgv.Rows[index].Cells[dgv.Columns[i].Name].Value = strValue;
                            }
                        }
                    }
                }
                if (Equals(type, "TransCache"))
                    foreach (var item in CommonTranslate.dicTransCache.Keys)
                    {
                        if (dicTmp.ContainsKey(item))
                        {
                            continue;
                        }
                        int index = dgv.Rows.Add();
                        for (int i = 0; i < dgv.Columns.Count; i++)
                        {
                            if (Equals(dgv.Columns[i].Name, StrBaseLanguage))
                                dgv.Rows[index].Cells[dgv.Columns[i].Name].Value = item;
                            else
                            {
                                CommonTranslate.dicTransCache[item].TryGetValue(dgv.Columns[i].Name, out string strValue);
                                if (string.IsNullOrEmpty(strValue) && DicAllCulture.ContainsKey(item))
                                    DicAllCulture[item].TryGetValue(dgv.Columns[i].Name, out strValue);
                                dgv.Rows[index].Cells[dgv.Columns[i].Name].Value = strValue;
                            }
                        }
                    }
            }
            else
            {
                foreach (string item in DicAllCulture.Keys)
                {
                    int index = dgv.Rows.Add();
                    for (int i = 0; i < dgv.Columns.Count; i++)
                    {
                        if (Equals(dgv.Columns[i].Name, StrBaseLanguage))
                            dgv.Rows[index].Cells[dgv.Columns[i].Name].Value = item;
                        else
                        {
                            DicAllCulture[item].TryGetValue(dgv.Columns[i].Name, out string strValue);
                            dgv.Rows[index].Cells[dgv.Columns[i].Name].Value = strValue;
                        }
                    }
                }
            }
        }

        static Dictionary<string, Dictionary<string, string>> DicAllCulture = new Dictionary<string, Dictionary<string, string>>();
        private static List<string> lstBingKeys = new List<string>() { "3DAEE5B978BA031557E739EE1E2A68CB1FAD5909", "708BEDCB01828123DC7B6C6A6AB12EF82DFBB611", "ABB1C5A823DC3B7B1D5F4BDB886ED308B50D1919", "F84955C82256C25518548EE0C161B0BF87681F2F", "AC56E0F30DC3119A55994244361E06DC1B777049", "6844AE3580856D2EC7A64C79F55F11AA47CB961B", "78280AF4DFA1CE1676AFE86340C690023A5AC139", "76518BFCEBBF18E107C7073FBD4A735001B56BB1", "73B027BB51D74FB461C097BCCF841DB5678FDBB3", "A4D660A48A6A97CCA791C34935E4C02BBB1BEC1C", "AFC76A66CF4F434ED080D245C30CF1E71C22959C", "3D8D4E1888B88B975484F0CA25CDD24AAC457ED8", "C21742C60D890BCA6B3819EDAD45B74C77A25658", "3C9778666C5BA4B406FFCBEE64EF478963039C51", "FF274CA911390CAF2E950A1930E5700DB887D8D7", "C9739C3837CBBD166870AF1C6EFFDEBE433DC2A8", "DB50E2E9FBE2E92B103E696DCF4E3E512A8826FB", "47D78E7A59A91431BD06A3D8D5496E6308634F46" };

        private static void InitAllCulture(Dictionary<string, string> dicTmp, string language)
        {
            foreach (var key in dicTmp.Keys)
            {
                if (!DicAllCulture.ContainsKey(key))
                {
                    DicAllCulture[key] = new Dictionary<string, string>();
                }
                DicAllCulture[key][language] = dicTmp[key];
            }
        }

        class BingTranslateInfo
        {
            public string TranslatedText { get; set; }
        }

        public static void LoadBaseCulture()
        {
            try
            {
                if (File.Exists("all.lang"))
                {
                    DicAllCulture = new Dictionary<string, Dictionary<string, string>>();
                    var dicTmp = File.ReadAllText("all.lang", Encoding.UTF8).DeserializeJson<Dictionary<string, Dictionary<string, string>>>();
                    if (dicTmp != null)
                        foreach (var key in dicTmp.Keys.OrderBy(p => p))
                        {
                            DicAllCulture[key] = dicTmp[key];
                        }
                }
            }
            catch (Exception oe)
            {
                Log.WriteError("LoadBaseCulture", oe);
            }
        }

        public static void SaveAllCulture()
        {
            if (DicAllCulture.Count <= 0) return;
            var sourcePath = Application.StartupPath + "\\SourceLang\\";
            if (!Directory.Exists(sourcePath))
                Directory.CreateDirectory(sourcePath);
            foreach (SupportedLanguage language in Enum.GetValues(typeof(SupportedLanguage)))
            {
                var cultureName = language.GetValue<MenuCategoryAttribute>();
                if (Equals(cultureName, StrBaseLanguage))
                {
                    continue;
                }
                var dicAll = new Dictionary<string, string>();
                foreach (var key in DicAllCulture.Keys)
                {
                    DicAllCulture[key].TryGetValue(cultureName, out var value);
                    if (!string.IsNullOrEmpty(value))
                        dicAll[key] = value;
                }
                if (dicAll.Count > 0)
                {
                    File.WriteAllText(sourcePath + cultureName + ".lang", CommonString.JavaScriptSerializer.Serialize(dicAll), Encoding.UTF8);
                }
            }
            File.WriteAllText(Application.StartupPath + "\\all.lang", CommonString.JavaScriptSerializer.Serialize(DicAllCulture), Encoding.UTF8);

            foreach (var file in Directory.GetFiles(sourcePath.Replace("SourceLang", "Lang")))
            {
                File.Delete(file);
            }
            foreach (var file in Directory.GetFiles(sourcePath.Replace("SourceLang", "Gzip")))
            {
                File.Delete(file);
            }

            var langFiles = Directory.GetFiles(sourcePath);
            foreach (var sourceLangFile in langFiles)
            {
                var copyFile = sourceLangFile.Replace("SourceLang", "Lang");
                File.Copy(sourceLangFile, copyFile);
                var gZipFile = sourceLangFile.Replace("SourceLang", "Gzip");
                GZip.GZip.Compress(Path.GetDirectoryName(copyFile), Path.GetDirectoryName(gZipFile), Path.GetFileName(gZipFile));
                File.Delete(copyFile);
            }
            foreach (var file in langFiles)
            {
                File.Move(file, file.Replace("SourceLang", "Lang"));
            }
            Directory.Delete(sourcePath);
        }

        public static Dictionary<string, string> TransLanguage(List<string> dicTmp, string tansType)
        {
            var dicResult = new Dictionary<string, string>();
            var batchSize = 100;
            var notTransValues = dicTmp.Select((x, index) => new { x, index })
                 .GroupBy(item => item.index / batchSize)
                 .Select(group => group.Select(item => item.x).ToArray())
                 .ToList();
            notTransValues.ForEach(val =>
            {
                var url = "https://api.microsofttranslator.com/V2/Ajax.svc/TranslateArray2?oncomplete=&appId=【【Key】】&to=" + tansType + "&texts=" + HttpUtility.UrlEncode(CommonString.JavaScriptSerializer.Serialize(val));
                var html = "";
                for (int i = 0; i < lstBingKeys.Count; i++)
                {
                    html = WebClientExt.GetHtml(url.Replace("【【Key】】", lstBingKeys.GetRndItem()));
                    if (!string.IsNullOrWhiteSpace(html) && html.StartsWith("["))
                    {
                        break;
                    }
                }

                if (string.IsNullOrWhiteSpace(html) || !html.StartsWith("["))
                {
                    html = WebClientExt.GetHtml(url.Replace("【【Key】】", lstBingKeys.GetRndItem()));
                }
                if (!string.IsNullOrWhiteSpace(html) && html.StartsWith("["))
                {
                    var lstTrans = html.DeserializeJson<List<BingTranslateInfo>>();
                    if (lstTrans.Count > 0 && lstTrans.Count == val.Length)
                    {
                        int index = 0;
                        foreach (var strItem in val)
                        {
                            dicResult[strItem] = lstTrans[index].TranslatedText;
                            index++;
                        }
                    }
                    //dicTmp[key][strItem] = html.Replace("\"", "").Trim();
                }
            });
            return dicResult;
        }
#endif

        public static string CurrentText(this string key, bool isNeedTrans = false)
        {
            if (string.IsNullOrEmpty(key))
                return key;
            if (!string.IsNullOrEmpty(NowLanguage) && DicStaticCulture.ContainsKey(key) && DicStaticCulture[key].ContainsKey(NowLanguage))
            {
                return DicStaticCulture[key][NowLanguage];
            }
            if (!DicNowCulture.ContainsKey(key))
            {
                var value = CommonTranslate.Translate(key, isNeedTrans);
                if (!string.IsNullOrEmpty(value) && !Equals(key, value))
                    DicNowCulture[key] = value;
            }
            DicNowCulture.TryGetValue(key, out var result);
            return string.IsNullOrEmpty(result) ? key : result;
        }

        public static string OriginText(this MetroForm form, Control ctrl)
        {
            return ctrl.AccessibleDescription ?? ctrl.Text;
        }

        public static string OriginText(this MetroForm form, ToolStripItem ctrl)
        {
            return ctrl.AccessibleDescription ?? ctrl.Text;
        }

        public static Image GetLanguageIcon(SupportedLanguage language)
        {
            Image icon;
            switch (language)
            {
                case SupportedLanguage.SimplifiedChinese:
                    icon = Resources.中文;
                    break;
                case SupportedLanguage.TraditionalChinese:
                    icon = Resources.中文;
                    break;
                case SupportedLanguage.English:
                    icon = Resources.英文;
                    break;
                case SupportedLanguage.Japanese:
                    icon = Resources.日语;
                    break;
                case SupportedLanguage.Korean:
                    icon = Resources.韩语;
                    break;
                case SupportedLanguage.French:
                    icon = Resources.法语;
                    break;
                case SupportedLanguage.German:
                    icon = Resources.德语;
                    break;
                case SupportedLanguage.Russian:
                    icon = Resources.俄语;
                    break;
                case SupportedLanguage.Portuguese:
                    icon = Resources.葡萄牙语;
                    break;
                case SupportedLanguage.Thai:
                case SupportedLanguage.Vietnamese:
                case SupportedLanguage.Spanish:
                case SupportedLanguage.Hebrew:
                case SupportedLanguage.Hungarian:
                case SupportedLanguage.Indonesian:
                case SupportedLanguage.Italian:
                case SupportedLanguage.Persian:
                case SupportedLanguage.Polish:
                case SupportedLanguage.Romanian:
                case SupportedLanguage.Turkish:
                case SupportedLanguage.Ukrainian:
                case SupportedLanguage.Arabic:
                case SupportedLanguage.Hindi:
                case SupportedLanguage.Serbian:
                case SupportedLanguage.Swedish:
                case SupportedLanguage.Malay:
                case SupportedLanguage.Dutch:
                case SupportedLanguage.Tamil:
                case SupportedLanguage.Czech:
                case SupportedLanguage.Khmer:
                case SupportedLanguage.Georgian:
                    icon = ImageProcessHelper.GetResourceImage(language.ToString());
                    break;
                default:
                    icon = Resources.自动;
                    break;
            }

            if (icon != null)
            {
                icon = ImageProcessHelper.ResizeImage(new Bitmap(icon), new Size(21, 21), true);
            }

            return icon;
        }
    }
}
